import type { NuiRecipeProps } from "@next-ui/styles";
import { forwardRef } from "react";

import type { buttonRecipe } from "../recipes/button";
import { cx } from "../utils/cx";

export type NuiButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
  NuiRecipeProps<typeof buttonRecipe> & {
    /** Whether the button is in a loading state */
    loading?: boolean;
    /** Whether the button is selected (for toggle/group usage) */
    selected?: boolean;
    /** Whether the button contains only an icon (implies accessibility adjustments) */
    iconOnly?: boolean;
    /** Value for the button (for use in button groups or custom logic) */
    value?: string | number | string[];
  };

/**
 * `NuiButton` is a flexible button component that supports
 * dynamic styling via design tokens, loading states, icon-only buttons,
 * and accessibility enhancements.
 */
export const NuiButton = forwardRef<HTMLButtonElement, NuiButtonProps>(
  (props, ref) => {
    const {
      className,
      children,
      loading = false,
      selected = false,
      disabled = false,
      iconOnly = false,
      value,
      ...rest
    } = props;

    const ariaLabel =
      iconOnly && !props["aria-label"] ? "Icon button" : props["aria-label"];

    return (
      <button
        ref={ref}
        type="button"
        className={cx(className)}
        disabled={disabled || loading}
        aria-busy={loading}
        aria-pressed={selected}
        aria-label={ariaLabel}
        data-icon={iconOnly}
        value={value}
        {...rest}
      >
        {loading ? <span className="loader" /> : children}
      </button>
    );
  },
);

NuiButton.displayName = "NuiButton";
