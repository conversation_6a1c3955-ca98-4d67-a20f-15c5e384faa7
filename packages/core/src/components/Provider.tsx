import type { NuiTheme } from "@next-ui/styles";
import {
  createContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import type { NuiContextProps, NuiProviderProps } from "../types";

export const NuiContext = createContext<NuiContextProps | undefined>(undefined);

export const NuiProvider = ({
  themes,
  initialTheme,
  initialMode,
  rootElement = "root",
  children,
}: NuiProviderProps) => {
  const [theme, setTheme] = useState<NuiTheme>();
  const [mode, setMode] = useState<string>();

  useEffect(() => {}, []);

  const changeTheme = useCallback(() => {}, []);

  const contextValue = useMemo(
    () => ({
      themes,
      theme,
      mode,
      changeTheme,
    }),
    [themes, theme, mode, changeTheme],
  );

  return (
    <NuiContext.Provider value={contextValue}>{children}</NuiContext.Provider>
  );
};
