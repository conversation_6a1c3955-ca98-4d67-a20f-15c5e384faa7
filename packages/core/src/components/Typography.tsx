import type { NuiRecipeProps } from "@next-ui/styles";
import { forwardRef } from "react";

import type { typographyRecipe } from "../recipes/typography";
import { cx } from "../utils/cx";

export type NuiTypographyProps = React.HTMLAttributes<HTMLElement> &
  NuiRecipeProps<typeof typographyRecipe> & {
    /** Map variants to specific HTML elements or React components. */
    variantMapping?: Record<string, React.ElementType>;
  };

export const NuiTypography = forwardRef<HTMLElement, NuiTypographyProps>(
  ({ className, children, variantMapping = {}, ...rest }) => {
    return (
      <p className={cx(className)} {...rest}>
        {children}
      </p>
    );
  },
);

NuiTypography.displayName = "NuiTypography";
