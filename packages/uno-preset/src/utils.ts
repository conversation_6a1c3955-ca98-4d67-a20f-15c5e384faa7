import { type NuiTheme, mapThemeToCSSVars } from "@next-ui/styles";

/**
 * Token categories supported for UnoCSS mapping.
 */
type TokenCategory = "text" | "background" | "border";

/**
 * Maps a `NuiTheme` to an UnoCSS-compatible theme format.
 */
export const mapThemeToUnoCSS = (theme: NuiTheme) => {
  const { colors, tokens } = mapThemeToCSSVars(theme);

  // Initialize base UnoCSS theme structure
  const unoCSSTheme = {
    colors,
    textColor: {},
    backgroundColor: {},
    borderColor: {},
  };

  // Link token categories to corresponding UnoCSS fields
  const tokenCategoryMapping = {
    text: unoCSSTheme.textColor,
    background: unoCSSTheme.backgroundColor,
    border: unoCSSTheme.borderColor,
  };

  // Populate UnoCSS fields with corresponding token values
  for (const [category, tokenValues] of Object.entries(tokens)) {
    const mappedCategory = category as TokenCategory;
    const target = tokenCategoryMapping[mappedCategory];

    if (target) Object.assign(target, tokenValues);
  }

  return unoCSSTheme;
};
