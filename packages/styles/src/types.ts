import type { colorModes } from "./theme/colorModes";
import type { colors } from "./theme/colors";
import type { designTokens } from "./theme/designTokens";
import type { variantTokens } from "./theme/variantTokens";

/**
 * Represents the foundational theme object for the UI system.
 */
export type NuiTheme = {
  /** The unique name of the theme. */
  name: string;
  /** The base color palette for the theme. */
  colors: typeof colors;
  /** Mode-specific color tokens. */
  modes: typeof colorModes;
  /** Tokens for design aspects (e.g., spacing, typography) */
  designTokens: typeof designTokens;
  /** Tokens for style customizations (e.g., size, appearance) */
  variantTokens: typeof variantTokens;
  /** CSS variables for the theme. */
  vars?: NuiThemeVars;
  /** Configuration for all components in the theme */
  components: Record<string, NuiRecipe>;
};

/**
 * Represents the CSS variables structure for the theme.
 */
export type NuiThemeVars = {
  /** Color-related CSS variables. Supports both flat and nested structures. */
  colors: Record<string, unknown>;
  /** Token-based CSS variables, structured as nested key-value pairs. */
  tokens: Record<string, unknown>;
};

/**
 * Defines a customizable theme configuration where only `name` is required.
 * Other properties are optional to allow for partial theme overrides.
 */
export type NuiThemeOptions = Partial<NuiTheme> & {
  /** The unique name of the theme override. */
  name: string;
};

/**
 * Defines the structure of a component's recipe, which includes base styles,
 * part-specific styles, and style variants.
 */
export type NuiRecipe = {
  /** Base classes applied to the component */
  base?: string[];
  /** Classes for specific parts of the component (e.g., input, label) */
  parts?: Record<string, string[]>;
  /** Additional props for specific parts of the component */
  partsProps?: Record<string, Record<string, unknown>>;
  /** Style variants available for the component (e.g., size, color scheme) */
  variants?: Record<string, Record<string, NuiVariantStyles>>;
  /** Default props for the component */
  defaultProps?: Record<string, unknown>;
};

/**
 * Utility type to derive props for a component recipe.
 * Handles variants, parts, base styles, and default properties.
 */
export type NuiRecipeProps<T extends NuiRecipe> = {
  /** Map variants to their allowed values */
  [K in keyof T["variants"]]?: keyof T["variants"][K];
} & {
  /** Optional classes for individual parts of the component */
  parts?: T["parts"] extends Record<string, unknown>
    ? { [P in keyof T["parts"]]?: string | string[] }
    : never;
} & {
  /** Base classes for the component */
  base?: T["base"];
} & {
  /** Default props constrained to match available variants or component-specific props */
  defaultProps?: Partial<T["defaultProps"]>;
};

/**
 * Defines the structure of available options for a specific variant.
 * Variants may include simple values, arrays, or nested configuration objects.
 */
export type NuiVariantStyles =
  | {
      base?: string | string[];
      parts?: Record<string, string | string[]>;
    }
  | string
  | string[];
