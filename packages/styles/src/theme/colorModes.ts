import { colors } from "./colors";

const light = {
  primary: {
    base: colors.blue[600],
    hover: colors.blue[700],
    active: colors.blue[800],
    subtle: colors.blue[200],
    background: colors.blue[100],
  },
  accent: {
    base: colors.indigo[600],
    hover: colors.indigo[700],
    active: colors.indigo[800],
    border: colors.indigo[200],
    subtle: colors.indigo[100],
    background: colors.indigo[50],
  },
  positive: {
    base: colors.emerald[600],
    hover: colors.emerald[700],
    active: colors.emerald[800],
    border: colors.emerald[200],
    subtle: colors.emerald[100],
    background: colors.emerald[50],
  },
  negative: {
    base: colors.red[600],
    hover: colors.red[700],
    active: colors.red[800],
    border: colors.red[200],
    subtle: colors.red[100],
    background: colors.red[50],
  },
  info: {
    base: colors.sky[500],
    hover: colors.sky[600],
    active: colors.sky[700],
    border: colors.sky[200],
    subtle: colors.sky[100],
    background: colors.sky[50],
  },
  text: {
    primary: colors.slate[900],
    secondary: colors.slate[400],
    link: colors.blue[600],
    linkHover: colors.blue[700],
    disabled: colors.gray[400],
  },
  background: {
    primary: colors.slate[100],
    secondary: colors.slate[300],
    surface: colors.slate[50],
    disabled: colors.gray[200],
  },
  border: {
    default: colors.slate[200],
    light: colors.slate[300],
    medium: colors.slate[400],
    hover: colors.slate[700],
    focus: colors.blue[600],
    disabled: colors.gray[400],
  },
};

const dark = {
  primary: {
    base: colors.blue[600],
    hover: colors.blue[500],
    active: colors.blue[400],
    subtle: colors.blue[700],
    background: colors.blue[800],
  },
  accent: {
    base: colors.indigo[600],
    hover: colors.indigo[500],
    active: colors.indigo[400],
    border: colors.indigo[200],
    subtle: colors.indigo[100],
    background: colors.indigo[50],
  },
  positive: {
    base: colors.emerald[600],
    hover: colors.emerald[500],
    active: colors.emerald[400],
    border: colors.emerald[200],
    subtle: colors.emerald[100],
    background: colors.emerald[50],
  },
  negative: {
    base: colors.red[600],
    hover: colors.red[500],
    active: colors.red[400],
    border: colors.red[200],
    subtle: colors.red[100],
    background: colors.red[50],
  },
  info: {
    base: colors.sky[700],
    hover: colors.sky[600],
    active: colors.sky[500],
    border: colors.sky[200],
    subtle: colors.sky[100],
    background: colors.sky[50],
  },
  text: {
    primary: colors.slate[200],
    secondary: colors.slate[400],
    link: colors.blue[400],
    linkHover: colors.blue[500],
    disabled: colors.gray[600],
  },
  background: {
    primary: colors.slate[900],
    secondary: colors.slate[800],
    surface: colors.slate[700],
    disabled: colors.gray[700],
  },
  border: {
    default: colors.slate[600],
    light: colors.slate[500],
    medium: colors.slate[400],
    hover: colors.slate[300],
    focus: colors.blue[400],
    disabled: colors.gray[600],
  },
};

export const colorModes = {
  light,
  dark,
};
