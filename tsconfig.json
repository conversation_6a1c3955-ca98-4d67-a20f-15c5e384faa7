{"compilerOptions": {"lib": ["ESNext", "DOM"], "target": "ESNext", "module": "ESNext", "moduleDetection": "force", "jsx": "react-jsx", "allowJs": true, "moduleResolution": "node", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "noEmit": true, "strict": true, "skipLibCheck": true, "noFallthroughCasesInSwitch": true, "noUnusedLocals": false, "noUnusedParameters": false, "noPropertyAccessFromIndexSignature": false}, "include": ["apps", "packages", "apps/client/uno.config.ts"], "exclude": ["node_modules", "packages/*/dist"]}