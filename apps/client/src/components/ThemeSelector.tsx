import { NuiTypography, useTheme } from "@next-ui/core";

export const ThemeSelector = () => {
  const { themes, theme, mode, changeTheme, changeMode } = useTheme();

  return (
    <div className="space-y-2">
      {/* Theme Selector */}
      <span className="text-xs text-gray-500">Theme</span>
      <select
        className="w-full p-1 rounded border border-gray-300 bg-[var(--nui-background)] text-[var(--nui-text)]"
        value={theme.name}
        onChange={(e) => changeTheme(e.target.value)}
      >
        {themes.map((theme) => (
          <option key={theme.name} value={theme.name}>
            {theme.name.charAt(0).toUpperCase() + theme.name.slice(1)}
          </option>
        ))}
      </select>

      {/* Mode Selector */}
      <span className="text-xs text-gray-500">Mode</span>
      <select
        className="w-full p-1 rounded border border-gray-300 bg-[var(--nui-background)] text-[var(--nui-text)]"
        value={mode}
        onChange={(e) => changeMode(e.target.value as string)}
      >
        {Object.keys(theme.modes).map((mode) => (
          <option key={mode} value={mode}>
            {mode.charAt(0).toUpperCase() + mode.slice(1)}
          </option>
        ))}
      </select>
    </div>
  );
};
