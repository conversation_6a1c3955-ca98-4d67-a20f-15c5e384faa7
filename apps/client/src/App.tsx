import { NuiButton, NuiTypography, useTheme } from "@next-ui/core";

import { ThemeSelector } from "./components";

export const App = () => {
  const { theme, mode } = useTheme();

  return (
    <div className="p-4 max-w-sm mx-auto bg-surface h-screen">
      <NuiTypography variant="title1">Next UI</NuiTypography>
      <NuiTypography variant="caption1">{`${theme.name} • ${mode}`}</NuiTypography>
      <NuiButton className="my-5">Button</NuiButton>
      <ThemeSelector />
    </div>
  );
};
