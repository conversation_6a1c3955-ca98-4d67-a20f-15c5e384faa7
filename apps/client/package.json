{"name": "client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "start": "bunx serve dist"}, "dependencies": {"@next-ui/core": "*", "@unocss/reset": "^66.1.0-beta.6", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@next-ui/uno-preset": "*", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@unocss/preset-mini": "^66.1.0-beta.3", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.8.2", "unocss": "^66.1.0-beta.3", "vite": "^6.2.1"}}