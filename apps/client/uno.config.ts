import { preset<PERSON><PERSON>t<PERSON> } from "@next-ui/uno-preset";
import { defineConfig, preset<PERSON>ini, transformerVariantGroup } from "unocss";

export default defineConfig({
  presets: [presetMini(), presetNextUI()],
  // Enable the variant group transformer for grouping CSS variants
  transformers: [transformerVariantGroup()],
  // Specify the content files to include in the CSS generation pipeline
  content: {
    pipeline: {
      include: [
        // Include files for UnoCSS processing
        /\.(jsx?|tsx?|mdx?|html)($|\?)/,
      ],
    },
  },
});
